<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.vw_account_loan.mapper.VwAccountLoanMapper">

    <resultMap type="VwAccountLoan" id="VwAccountLoanResult">
        <result property="id"    column="id"    />
        <result property="applyId"    column="apply_id"    />
        <result property="putoutId"    column="putout_id"    />
        <result property="contractId"    column="contract_id"    />
        <result property="customerId"    column="customer_id"    />
        <result property="customerName"    column="customer_name"    />
        <result property="repaymentStatus"    column="repayment_status"    />
        <result property="loanStatus"    column="loan_status"    />
        <result property="occurType"    column="occur_type"    />
        <result property="productId"    column="product_id"    />
        <result property="partnerId"    column="partner_id"    />
        <result property="currency"    column="currency"    />
        <result property="businessSum"    column="business_sum"    />
        <result property="contractAmt"    column="contract_amt"    />
        <result property="putoutDate"    column="putout_date"    />
        <result property="billDate"    column="bill_date"    />
        <result property="firstRepayDate"    column="first_repay_date"    />
        <result property="lastDueDate"    column="last_due_date"    />
        <result property="nextDueDate"    column="next_due_date"    />
        <result property="maturityDate"    column="maturity_date"    />
        <result property="term"    column="term"    />
        <result property="termUnit"    column="term_unit"    />
        <result property="settleDate"    column="settle_date"    />
        <result property="finishDate"    column="finish_date"    />
        <result property="originalMaturityDate"    column="original_maturity_date"    />
        <result property="rateTermId"    column="rate_term_id"    />
        <result property="rptTermId"    column="rpt_term_id"    />
        <result property="finTermId"    column="fin_term_id"    />
        <result property="feeTermId"    column="fee_term_id"    />
        <result property="normalBalance"    column="normal_balance"    />
        <result property="nextInstalmentAmt"    column="next_instalment_amt"    />
        <result property="currentPeriod"    column="current_period"    />
        <result property="currentBalance"    column="current_balance"    />
        <result property="currenctinteBalance"    column="currenctinte_balance"    />
        <result property="overdueBalance"    column="overdue_balance"    />
        <result property="odinteBalance"    column="odinte_balance"    />
        <result property="fineinteBalance"    column="fineinte_balance"    />
        <result property="compdinteBalance"    column="compdinte_balance"    />
        <result property="overdueAmt"    column="overdue_amt"    />
        <result property="overdueDays"    column="overdue_days"    />
        <result property="lcaTimes"    column="lca_times"    />
        <result property="totalPeriod"    column="total_period"    />
        <result property="graceinteBalance"    column="graceinte_balance"    />
        <result property="accrueinteBalance"    column="accrueinte_balance"    />
        <result property="repriceType"    column="reprice_type"    />
        <result property="repriceFlag"    column="reprice_flag"    />
        <result property="repriceCycle"    column="reprice_cycle"    />
        <result property="repriceDate"    column="reprice_date"    />
        <result property="lastRepriceDate"    column="last_reprice_date"    />
        <result property="nextRepriceDate"    column="next_reprice_date"    />
        <result property="graceDays"    column="grace_days"    />
        <result property="loanOverDateFlag"    column="loan_over_date_flag"    />
        <result property="holidayPaymentFlag"    column="holiday_payment_flag"    />
        <result property="autoPayFlag"    column="auto_pay_flag"    />
        <result property="interestTypeFlag"    column="interest_type_flag"    />
        <result property="classifyResult"    column="classify_result"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="lockFlag"    column="lock_flag"    />
        <result property="businessDate"    column="business_date"    />
        <result property="putoutRate"    column="putout_rate"    />
        <result property="putoutPaymentMethod"    column="putout_payment_method"    />
        <result property="loanRate"    column="loan_rate"    />
        <result property="managementFeeRate"    column="management_fee_rate"    />
        <result property="paymentFlag"    column="payment_flag"    />
        <result property="performanceStatus"    column="performance_status"    />
        <result property="orgId"    column="org_id"    />
        <result property="officeId"    column="office_id"    />
        <result property="userId"    column="user_id"    />
        <result property="mangerId"    column="manger_id"    />
        <result property="allotStatus"    column="allot_status"    />
        <result property="allotAssignee"    column="allot_assignee"    />
        <result property="litigationStatus"    column="litigation_status"    />
        <result property="subType"    column="sub_type"    />
        <result property="numberStatus"    column="number_status"    />
        <result property="lagFlag"    column="lag_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="fOverdueAmount"    column="F_overdue_amount"    />
        <result property="bOverdueDays"    column="B_overdue_days"    />
        <result property="bOverdueAmount"    column="B_overdue_amount"    />
        <result property="dOverdueDays"    column="D_overdue_days"    />
        <result property="dOverdueAmount"    column="D_overdue_amount"    />
        <result property="bRepaymentDate"    column="B_repayment_date"    />
        <result property="dRepaymentDate"    column="D_repayment_date"    />
        <result property="bPeriods"    column="B_periods"    />
        <result property="dPeriods"    column="D_periods"    />
        <result property="bRemainingAmounts"    column="B_remaining_amounts"    />
        <result property="dRemainingAmounts"    column="D_remaining_amounts"    />
        <result property="bCurrentPeriods"    column="B_current_periods"    />
        <result property="dCurrentPeriods"    column="D_current_periods"    />
        <result property="bRepaymentAmounts"    column="B_repayment_amounts"    />
        <result property="dRepaymentAmounts"    column="D_repayment_amounts"    />
        <result property="bNowMoney"    column="B_now_money"    />
        <result property="dNowMoney"    column="D_now_money"    />
        <result property="bReturnTime"    column="B_return_time"    />
        <result property="dReturnTime"    column="D_return_time"    />
        <result property="mobilePhone"    column="mobile_phone"    />
        <result property="nickName"    column="nick_name"    />
        <result property="jgName"    column="jg_name"    />
        <result property="jgStatus"    column="jg_status"    />
        <result property="orgName"    column="org_name"    />
        <result property="plateNo"    column="plate_no"    />
        <result property="loanId"    column="loan_id"    />
        <result property="slippageStatus"    column="slippage_status"    />
        <result property="followUp"    column="follow_up"    />
        <result property="followStatus"    column="follow_status"    />
        <result property="realReturnMoney"    column="real_return_money"    />
        <result property="reminderDate"    column="reminder_date"    />
        <result property="certId"    column="cert_id"    />
        <result property="carStatus"    column="car_status"    />
        <result property="gpsStatus"    column="gps_status"    />
        <result property="carDetailAddress"    column="car_detail_address"    />
        <result property="productName"    column="product_name"    />
        <result property="isExtension"    column="is_extension"    />
        <result property="urgeUser"    column="urge_user"    />
        <result property="urgeTime"    column="urge_time"    />
        <result property="dhAssignmentType"    column="dh_assignment_type"    />
        <result property="urgeAssignmentType"    column="urge_assignment_type"    />
        <result property="petitionAssignmentType"    column="petition_assignment_type"    />
        <result property="lawAssignmentType"    column="law_assignment_type"    />
        <result property="petitionUser"    column="petition_user"    />
        <result property="followTime"    column="follow_time"    />
        <result property="dSettleAmount" column="dSettleAmount" />
        <result property="bSettleAmount" column="bSettleAmount" />
        <result property="trackingTime" column="tracking_time" />
        <result property="appointedTime" column="appointed_time" />
        <result property="extensionStatus" column="extensionStatus" />
        <result property="carTeamName" column="carTeamName" />
        <result property="overdueCount" column="overdueCount" />
    </resultMap>


    <sql id="selectVwAccountLoanVo">
        SELECT
            a.*,
            s.total_money AS bSettleAmount,
            r.appointed_time,
            r.tracking_time,
            (
                SELECT COUNT(*)
                FROM vw_account_loan v
                WHERE v.apply_id = a.apply_id
            ) AS overdueCount
        FROM
            vw_account_loan a
                LEFT JOIN LATERAL (
                SELECT
                    total_money
                FROM
                    loan_settle
                WHERE
                    loan_id = a.loan_id
                ORDER BY
                    create_date DESC
                    LIMIT 1
    ) s ON true
            LEFT JOIN LATERAL (
            SELECT
            appointed_time,
            tracking_time
            FROM
            loan_reminder
            WHERE
            loan_id = a.loan_id
            ORDER BY
            create_time DESC
            LIMIT 1
            ) r ON true
    </sql>


    <select id="selectVwAccountLoanList" parameterType="VwAccountLoan" resultMap="VwAccountLoanResult">
        <include refid="selectVwAccountLoanVo"/>
        <where>
            <if test="applyId != null  and applyId != ''"> and apply_id = #{applyId}</if>
            <if test="loanId != null  and loanId != ''"> and loan_id = #{loanId}</if>
            <if test="putoutId != null  and putoutId != ''"> and putout_id = #{putoutId}</if>
            <if test="contractId != null  and contractId != ''"> and contract_id = #{contractId}</if>
            <if test="customerId != null  and customerId != ''"> and customer_id = #{customerId}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="loanStatus != null  and loanStatus != ''"> and loan_status = #{loanStatus}</if>
            <if test="repaymentStatus != null  and repaymentStatus != ''"> and repayment_status = #{repaymentStatus}</if>
            <if test="occurType != null  and occurType != ''"> and occur_type = #{occurType}</if>
            <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
            <if test="partnerId != null  and partnerId != ''"> and partner_id = #{partnerId}</if>
            <if test="currency != null  and currency != ''"> and currency = #{currency}</if>
            <if test="businessSum != null "> and business_sum = #{businessSum}</if>
            <if test="contractAmt != null "> and contract_amt = #{contractAmt}</if>
            <if test="putoutDate != null "> and putout_date = #{putoutDate}</if>
            <if test="billDate != null "> and bill_date = #{billDate}</if>
            <if test="firstRepayDate != null "> and first_repay_date = #{firstRepayDate}</if>
            <if test="lastDueDate != null "> and last_due_date = #{lastDueDate}</if>
            <if test="nextDueDate != null "> and next_due_date = #{nextDueDate}</if>
            <if test="maturityDate != null "> and maturity_date = #{maturityDate}</if>
            <if test="term != null "> and term = #{term}</if>
            <if test="termUnit != null  and termUnit != ''"> and term_unit = #{termUnit}</if>
            <if test="settleDate != null "> and settle_date = #{settleDate}</if>
            <if test="finishDate != null "> and finish_date = #{finishDate}</if>
            <if test="originalMaturityDate != null "> and original_maturity_date = #{originalMaturityDate}</if>
            <if test="rateTermId != null  and rateTermId != ''"> and rate_term_id = #{rateTermId}</if>
            <if test="rptTermId != null  and rptTermId != ''"> and rpt_term_id = #{rptTermId}</if>
            <if test="finTermId != null  and finTermId != ''"> and fin_term_id = #{finTermId}</if>
            <if test="feeTermId != null  and feeTermId != ''"> and fee_term_id = #{feeTermId}</if>
            <if test="normalBalance != null "> and normal_balance = #{normalBalance}</if>
            <if test="nextInstalmentAmt != null "> and next_instalment_amt = #{nextInstalmentAmt}</if>
            <if test="currentPeriod != null "> and current_period = #{currentPeriod}</if>
            <if test="currentBalance != null "> and current_balance = #{currentBalance}</if>
            <if test="currenctinteBalance != null "> and currenctinte_balance = #{currenctinteBalance}</if>
            <if test="overdueBalance != null "> and overdue_balance = #{overdueBalance}</if>
            <if test="odinteBalance != null "> and odinte_balance = #{odinteBalance}</if>
            <if test="fineinteBalance != null "> and fineinte_balance = #{fineinteBalance}</if>
            <if test="compdinteBalance != null "> and compdinte_balance = #{compdinteBalance}</if>
            <if test="overdueAmt != null "> and overdue_amt = #{overdueAmt}</if>
            <if test="overdueDays != null "> and overdue_days = #{overdueDays}</if>
            <if test="lcaTimes != null "> and lca_times = #{lcaTimes}</if>
            <if test="totalPeriod != null "> and total_period = #{totalPeriod}</if>
            <if test="graceinteBalance != null "> and graceinte_balance = #{graceinteBalance}</if>
            <if test="accrueinteBalance != null "> and accrueinte_balance = #{accrueinteBalance}</if>
            <if test="repriceType != null  and repriceType != ''"> and reprice_type = #{repriceType}</if>
            <if test="repriceFlag != null  and repriceFlag != ''"> and reprice_flag = #{repriceFlag}</if>
            <if test="repriceCycle != null "> and reprice_cycle = #{repriceCycle}</if>
            <if test="repriceDate != null "> and reprice_date = #{repriceDate}</if>
            <if test="lastRepriceDate != null "> and last_reprice_date = #{lastRepriceDate}</if>
            <if test="nextRepriceDate != null "> and next_reprice_date = #{nextRepriceDate}</if>
            <if test="graceDays != null "> and grace_days = #{graceDays}</if>
            <if test="loanOverDateFlag != null  and loanOverDateFlag != ''"> and loan_over_date_flag = #{loanOverDateFlag}</if>
            <if test="holidayPaymentFlag != null  and holidayPaymentFlag != ''"> and holiday_payment_flag = #{holidayPaymentFlag}</if>
            <if test="autoPayFlag != null  and autoPayFlag != ''"> and auto_pay_flag = #{autoPayFlag}</if>
            <if test="interestTypeFlag != null  and interestTypeFlag != ''"> and interest_type_flag = #{interestTypeFlag}</if>
            <if test="classifyResult != null  and classifyResult != ''"> and classify_result = #{classifyResult}</if>
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="lockFlag != null  and lockFlag != ''"> and lock_flag = #{lockFlag}</if>
            <if test="businessDate != null "> and business_date = #{businessDate}</if>
            <if test="putoutRate != null "> and putout_rate = #{putoutRate}</if>
            <if test="putoutPaymentMethod != null  and putoutPaymentMethod != ''"> and putout_payment_method = #{putoutPaymentMethod}</if>
            <if test="loanRate != null "> and loan_rate = #{loanRate}</if>
            <if test="managementFeeRate != null "> and management_fee_rate = #{managementFeeRate}</if>
            <if test="paymentFlag != null  and paymentFlag != ''"> and payment_flag = #{paymentFlag}</if>
            <if test="performanceStatus != null  and performanceStatus != ''"> and performance_status = #{performanceStatus}</if>
            <if test="orgId != null  and orgId != ''"> and org_id = #{orgId}</if>
            <if test="officeId != null  and officeId != ''"> and office_id = #{officeId}</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="mangerId != null  and mangerId != ''"> and manger_id = #{mangerId}</if>
            <if test="allotStatus != null  and allotStatus != ''"> and allot_status = #{allotStatus}</if>
            <if test="allotAssignee != null  and allotAssignee != ''"> and allot_assignee = #{allotAssignee}</if>
            <if test="litigationStatus != null  and litigationStatus != ''"> and litigation_status = #{litigationStatus}</if>
            <if test="subType != null  and subType != ''"> and sub_type = #{subType}</if>
            <if test="numberStatus != null  and numberStatus != ''"> and number_status = #{numberStatus}</if>
            <if test="lagFlag != null  and lagFlag != ''"> and lag_flag = #{lagFlag}</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="fOverdueAmount != null "> and F_overdue_amount = #{fOverdueAmount}</if>
            <if test="bOverdueDays != null "> and B_overdue_days = #{bOverdueDays}</if>
            <if test="bOverdueAmount != null "> and B_overdue_amount = #{bOverdueAmount}</if>
            <if test="dOverdueDays != null "> and D_overdue_days = #{dOverdueDays}</if>
            <if test="dOverdueAmount != null "> and D_overdue_amount = #{dOverdueAmount}</if>
            <if test="bRepaymentDate != null "> and B_repayment_date = #{bRepaymentDate}</if>
            <if test="dRepaymentDate != null "> and D_repayment_date = #{dRepaymentDate}</if>
            <if test="bPeriods != null "> and B_periods = #{bPeriods}</if>
            <if test="dPeriods != null "> and D_periods = #{dPeriods}</if>
            <if test="bRemainingAmounts != null "> and B_remaining_amounts = #{bRemainingAmounts}</if>
            <if test="dRemainingAmounts != null "> and D_remaining_amounts = #{dRemainingAmounts}</if>
            <if test="bCurrentPeriods != null "> and B_current_periods = #{bCurrentPeriods}</if>
            <if test="dCurrentPeriods != null "> and D_current_periods = #{dCurrentPeriods}</if>
            <if test="bRepaymentAmounts != null "> and B_repayment_amounts = #{bRepaymentAmounts}</if>
            <if test="dRepaymentAmounts != null "> and D_repayment_amounts = #{dRepaymentAmounts}</if>
            <if test="bNowMoney != null "> and B_now_money = #{bNowMoney}</if>
            <if test="dNowMoney != null "> and D_now_money = #{dNowMoney}</if>
            <if test="bReturnTime != null "> and B_return_time = #{bReturnTime}</if>
            <if test="dReturnTime != null "> and D_return_time = #{dReturnTime}</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="jgName != null  and jgName != ''"> and jg_name like concat('%', #{jgName}, '%')</if>
            <if test="jgStatus != null  and jgStatus != ''"> and jg_status = #{jgStatus}</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="slippageStatus != null  and slippageStatus != ''"> and slippage_status = #{slippageStatus}</if>
            <if test="followUp != null  and followUp != ''"> and follow_up = #{followUp}</if>
            <if test="followStatus != null  and followStatus != ''"> and follow_status = #{followStatus}</if>
            <if test="certId != null  and certId != ''"> and cert_id = #{certId}</if>
            <if test="carStatus != null  and carStatus != ''"> and car_status = #{carStatus}</if>
            <if test="gpsStatus != null  and gpsStatus != ''"> and gps_status = #{gpsStatus}</if>
            <if test="carDetailAddress != null  and carDetailAddress != ''"> and car_detail_address = #{carDetailAddress}</if>
            <if test="productName != null and productName != ''"> AND product_name = #{productName}</if>
              <if test="isExtension != null and isExtension != ''"> AND is_extension = #{isExtension}</if>
              <if test="urgeUser != null and urgeUser != ''"> AND urge_user = #{urgeUser}</if>
              <if test="urgeTime != null and urgeTime != ''"> AND urge_time = #{urgeTime}</if>
            <!-- 新增 appointed_time 区间筛选，确保健壮性和注释清晰 -->
            <if test="startTime != null and startTime != ''"> and r.appointed_time &gt;= #{startTime} </if>
            <if test="endTime != null and endTime != ''"> and r.appointed_time &lt;= #{endTime} </if>
            <if test="dhAssignmentType != null"> and dh_assignment_type = #{dhAssignmentType}</if>
            <if test="urgeAssignmentType != null"> and urge_assignment_type = #{urgeAssignmentType}</if>
            <if test="petitionAssignmentType != null"> and petition_assignment_type = #{petitionAssignmentType}</if>
            <if test="lawAssignmentType != null"> and law_assignment_type = #{lawAssignmentType}</if>
            <if test="petitionUser != null and petitionUser != ''"> and petition_user = #{petitionUser}</if>
            <if test="carTeamName != null and carTeamName != ''"> and car_team_name = #{carTeamName}</if>
        </where>
        <if test="isFindCar != null">
            <choose>
                <when test="isFindCar == 0">
                    HAVING (carTeamName IS NULL OR carTeamName = '')
                </when>
                <when test="isFindCar == 1">
                    HAVING carTeamName IS NOT NULL AND carTeamName != ''
                </when>
            </choose>
        </if>
        ORDER BY a.overdue_days DESC
    </select>


    <select id="selectVwAccountLoanExtensionList" parameterType="VwAccountLoan" resultMap="VwAccountLoanResult">
        SELECT
            a.*,
            e.status AS extensionStatus
        FROM
            vw_account_loan a
        JOIN loan_extension e ON a.loan_id = e.loan_id
        <where>
            <if test="customerName != null and customerName != ''">
                and a.customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="certId != null and certId != ''">
                and a.cert_id like concat('%', #{certId}, '%')
            </if>
            <if test="plateNo != null and plateNo != ''">
                and a.plate_no like concat('%', #{plateNo}, '%')
            </if>
            <if test="nickName != null and nickName != ''">
                and a.nick_name like concat('%', #{nickName}, '%')
            </if>
            <if test="jgName != null and jgName != ''">
                and a.jg_name like concat('%', #{jgName}, '%')
            </if>
            <if test="orgName != null and orgName != ''">
                and a.org_name like concat('%', #{orgName}, '%')
            </if>
            <if test="partnerId != null and partnerId != ''">
                and a.partner_id = #{partnerId}
            </if>
            <if test="followUp != null and followUp != ''">
                and a.follow_up = #{followUp}
            </if>
            <if test="followStatus != null and followStatus != ''">
                and a.follow_status = #{followStatus}
            </if>
            <if test="startTime != null and startTime != ''">
                and a.create_date &gt;= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and a.create_date &lt;= #{endTime}
            </if>
        </where>
        ORDER BY a.overdue_days DESC
    </select>

    <select id="selectVwAccountLoanById" parameterType="String" resultMap="VwAccountLoanResult">
        <include refid="selectVwAccountLoanVo"/>
        where id = #{id}
    </select>

    <!--根据LoanId查询-->
    <select id="selectVwAccountLoanByLoanId" parameterType="String" resultMap="VwAccountLoanResult">
        <include refid="selectVwAccountLoanVo"/>
        where loan_id = #{loanId}
    </select>

    <insert id="insertVwAccountLoan" parameterType="VwAccountLoan">
        insert into vw_account_loan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyId != null">apply_id,</if>
            <if test="putoutId != null">putout_id,</if>
            <if test="contractId != null">contract_id,</if>
            <if test="customerId != null">customer_id,</if>
            <if test="customerName != null">customer_name,</if>
            <if test="loanStatus != null">loan_status,</if>
            <if test="occurType != null">occur_type,</if>
            <if test="productId != null">product_id,</if>
            <if test="partnerId != null">partner_id,</if>
            <if test="currency != null">currency,</if>
            <if test="businessSum != null">business_sum,</if>
            <if test="contractAmt != null">contract_amt,</if>
            <if test="putoutDate != null">putout_date,</if>
            <if test="billDate != null">bill_date,</if>
            <if test="firstRepayDate != null">first_repay_date,</if>
            <if test="lastDueDate != null">last_due_date,</if>
            <if test="nextDueDate != null">next_due_date,</if>
            <if test="maturityDate != null">maturity_date,</if>
            <if test="term != null">term,</if>
            <if test="termUnit != null">term_unit,</if>
            <if test="settleDate != null">settle_date,</if>
            <if test="finishDate != null">finish_date,</if>
            <if test="originalMaturityDate != null">original_maturity_date,</if>
            <if test="rateTermId != null">rate_term_id,</if>
            <if test="rptTermId != null">rpt_term_id,</if>
            <if test="finTermId != null">fin_term_id,</if>
            <if test="feeTermId != null">fee_term_id,</if>
            <if test="normalBalance != null">normal_balance,</if>
            <if test="nextInstalmentAmt != null">next_instalment_amt,</if>
            <if test="currentPeriod != null">current_period,</if>
            <if test="currentBalance != null">current_balance,</if>
            <if test="currenctinteBalance != null">currenctinte_balance,</if>
            <if test="overdueBalance != null">overdue_balance,</if>
            <if test="odinteBalance != null">odinte_balance,</if>
            <if test="fineinteBalance != null">fineinte_balance,</if>
            <if test="compdinteBalance != null">compdinte_balance,</if>
            <if test="overdueAmt != null">overdue_amt,</if>
            <if test="overdueDays != null">overdue_days,</if>
            <if test="lcaTimes != null">lca_times,</if>
            <if test="totalPeriod != null">total_period,</if>
            <if test="graceinteBalance != null">graceinte_balance,</if>
            <if test="accrueinteBalance != null">accrueinte_balance,</if>
            <if test="repriceType != null">reprice_type,</if>
            <if test="repriceFlag != null">reprice_flag,</if>
            <if test="repriceCycle != null">reprice_cycle,</if>
            <if test="repriceDate != null">reprice_date,</if>
            <if test="lastRepriceDate != null">last_reprice_date,</if>
            <if test="nextRepriceDate != null">next_reprice_date,</if>
            <if test="graceDays != null">grace_days,</if>
            <if test="loanOverDateFlag != null">loan_over_date_flag,</if>
            <if test="holidayPaymentFlag != null">holiday_payment_flag,</if>
            <if test="autoPayFlag != null">auto_pay_flag,</if>
            <if test="interestTypeFlag != null">interest_type_flag,</if>
            <if test="classifyResult != null">classify_result,</if>
            <if test="batchNo != null">batch_no,</if>
            <if test="lockFlag != null">lock_flag,</if>
            <if test="businessDate != null">business_date,</if>
            <if test="putoutRate != null">putout_rate,</if>
            <if test="putoutPaymentMethod != null">putout_payment_method,</if>
            <if test="loanRate != null">loan_rate,</if>
            <if test="managementFeeRate != null">management_fee_rate,</if>
            <if test="paymentFlag != null">payment_flag,</if>
            <if test="performanceStatus != null">performance_status,</if>
            <if test="orgId != null">org_id,</if>
            <if test="officeId != null">office_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="mangerId != null">manger_id,</if>
            <if test="allotStatus != null">allot_status,</if>
            <if test="allotAssignee != null">allot_assignee,</if>
            <if test="litigationStatus != null">litigation_status,</if>
            <if test="subType != null">sub_type,</if>
            <if test="numberStatus != null">number_status,</if>
            <if test="lagFlag != null">lag_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="fOverdueAmount != null">F_overdue_amount,</if>
            <if test="bOverdueDays != null">B_overdue_days,</if>
            <if test="bOverdueAmount != null">B_overdue_amount,</if>
            <if test="dOverdueDays != null">D_overdue_days,</if>
            <if test="dOverdueAmount != null">D_overdue_amount,</if>
            <if test="bRepaymentDate != null">B_repayment_date,</if>
            <if test="dRepaymentDate != null">D_repayment_date,</if>
            <if test="bPeriods != null">B_periods,</if>
            <if test="dPeriods != null">D_periods,</if>
            <if test="bRemainingAmounts != null">B_remaining_amounts,</if>
            <if test="dRemainingAmounts != null">D_remaining_amounts,</if>
            <if test="bCurrentPeriods != null">B_current_periods,</if>
            <if test="dCurrentPeriods != null">D_current_periods,</if>
            <if test="bRepaymentAmounts != null">B_repayment_amounts,</if>
            <if test="dRepaymentAmounts != null">D_repayment_amounts,</if>
            <if test="bNowMoney != null">B_now_money,</if>
            <if test="dNowMoney != null">D_now_money,</if>
            <if test="bReturnTime != null">B_return_time,</if>
            <if test="dReturnTime != null">D_return_time,</if>
            <if test="mobilePhone != null">mobile_phone,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="jgName != null">jg_name,</if>
            <if test="jgStatus != null">jg_status,</if>
            <if test="orgName != null">org_name,</if>
            <if test="plateNo != null">plate_no,</if>
            <if test="slippageStatus != null">slippage_status,</if>
            <if test="followUp != null">follow_up,</if>
            <if test="followStatus != null">follow_status,</if>
            <if test="certId != null">cert_id,</if>
            <if test="carStatus != null">car_status,</if>
            <if test="gpsStatus != null">gps_status,</if>
            <if test="carDetailAddress != null">car_detail_address,</if>
            <if test="urgeUser != null">urge_user,</if>
            <if test="urgeTime != null">urge_time,</if>
            <if test="dhAssignmentType != null">dh_assignment_type,</if>
            <if test="urgeAssignmentType != null">urge_assignment_type,</if>
            <if test="petitionAssignmentType != null">petition_assignment_type,</if>
            <if test="lawAssignmentType != null">law_assignment_type,</if>
            <if test="petitionUser != null">petition_user,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyId != null">#{applyId},</if>
            <if test="putoutId != null">#{putoutId},</if>
            <if test="contractId != null">#{contractId},</if>
            <if test="customerId != null">#{customerId},</if>
            <if test="customerName != null">#{customerName},</if>
            <if test="loanStatus != null">#{loanStatus},</if>
            <if test="occurType != null">#{occurType},</if>
            <if test="productId != null">#{productId},</if>
            <if test="partnerId != null">#{partnerId},</if>
            <if test="currency != null">#{currency},</if>
            <if test="businessSum != null">#{businessSum},</if>
            <if test="contractAmt != null">#{contractAmt},</if>
            <if test="putoutDate != null">#{putoutDate},</if>
            <if test="billDate != null">#{billDate},</if>
            <if test="firstRepayDate != null">#{firstRepayDate},</if>
            <if test="lastDueDate != null">#{lastDueDate},</if>
            <if test="nextDueDate != null">#{nextDueDate},</if>
            <if test="maturityDate != null">#{maturityDate},</if>
            <if test="term != null">#{term},</if>
            <if test="termUnit != null">#{termUnit},</if>
            <if test="settleDate != null">#{settleDate},</if>
            <if test="finishDate != null">#{finishDate},</if>
            <if test="originalMaturityDate != null">#{originalMaturityDate},</if>
            <if test="rateTermId != null">#{rateTermId},</if>
            <if test="rptTermId != null">#{rptTermId},</if>
            <if test="finTermId != null">#{finTermId},</if>
            <if test="feeTermId != null">#{feeTermId},</if>
            <if test="normalBalance != null">#{normalBalance},</if>
            <if test="nextInstalmentAmt != null">#{nextInstalmentAmt},</if>
            <if test="currentPeriod != null">#{currentPeriod},</if>
            <if test="currentBalance != null">#{currentBalance},</if>
            <if test="currenctinteBalance != null">#{currenctinteBalance},</if>
            <if test="overdueBalance != null">#{overdueBalance},</if>
            <if test="odinteBalance != null">#{odinteBalance},</if>
            <if test="fineinteBalance != null">#{fineinteBalance},</if>
            <if test="compdinteBalance != null">#{compdinteBalance},</if>
            <if test="overdueAmt != null">#{overdueAmt},</if>
            <if test="overdueDays != null">#{overdueDays},</if>
            <if test="lcaTimes != null">#{lcaTimes},</if>
            <if test="totalPeriod != null">#{totalPeriod},</if>
            <if test="graceinteBalance != null">#{graceinteBalance},</if>
            <if test="accrueinteBalance != null">#{accrueinteBalance},</if>
            <if test="repriceType != null">#{repriceType},</if>
            <if test="repriceFlag != null">#{repriceFlag},</if>
            <if test="repriceCycle != null">#{repriceCycle},</if>
            <if test="repriceDate != null">#{repriceDate},</if>
            <if test="lastRepriceDate != null">#{lastRepriceDate},</if>
            <if test="nextRepriceDate != null">#{nextRepriceDate},</if>
            <if test="graceDays != null">#{graceDays},</if>
            <if test="loanOverDateFlag != null">#{loanOverDateFlag},</if>
            <if test="holidayPaymentFlag != null">#{holidayPaymentFlag},</if>
            <if test="autoPayFlag != null">#{autoPayFlag},</if>
            <if test="interestTypeFlag != null">#{interestTypeFlag},</if>
            <if test="classifyResult != null">#{classifyResult},</if>
            <if test="batchNo != null">#{batchNo},</if>
            <if test="lockFlag != null">#{lockFlag},</if>
            <if test="businessDate != null">#{businessDate},</if>
            <if test="putoutRate != null">#{putoutRate},</if>
            <if test="putoutPaymentMethod != null">#{putoutPaymentMethod},</if>
            <if test="loanRate != null">#{loanRate},</if>
            <if test="managementFeeRate != null">#{managementFeeRate},</if>
            <if test="paymentFlag != null">#{paymentFlag},</if>
            <if test="performanceStatus != null">#{performanceStatus},</if>
            <if test="orgId != null">#{orgId},</if>
            <if test="officeId != null">#{officeId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="mangerId != null">#{mangerId},</if>
            <if test="allotStatus != null">#{allotStatus},</if>
            <if test="allotAssignee != null">#{allotAssignee},</if>
            <if test="litigationStatus != null">#{litigationStatus},</if>
            <if test="subType != null">#{subType},</if>
            <if test="numberStatus != null">#{numberStatus},</if>
            <if test="lagFlag != null">#{lagFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="fOverdueAmount != null">#{fOverdueAmount},</if>
            <if test="bOverdueDays != null">#{bOverdueDays},</if>
            <if test="bOverdueAmount != null">#{bOverdueAmount},</if>
            <if test="dOverdueDays != null">#{dOverdueDays},</if>
            <if test="dOverdueAmount != null">#{dOverdueAmount},</if>
            <if test="bRepaymentDate != null">#{bRepaymentDate},</if>
            <if test="dRepaymentDate != null">#{dRepaymentDate},</if>
            <if test="bPeriods != null">#{bPeriods},</if>
            <if test="dPeriods != null">#{dPeriods},</if>
            <if test="bRemainingAmounts != null">#{bRemainingAmounts},</if>
            <if test="dRemainingAmounts != null">#{dRemainingAmounts},</if>
            <if test="bCurrentPeriods != null">#{bCurrentPeriods},</if>
            <if test="dCurrentPeriods != null">#{dCurrentPeriods},</if>
            <if test="bRepaymentAmounts != null">#{bRepaymentAmounts},</if>
            <if test="dRepaymentAmounts != null">#{dRepaymentAmounts},</if>
            <if test="bNowMoney != null">#{bNowMoney},</if>
            <if test="dNowMoney != null">#{dNowMoney},</if>
            <if test="bReturnTime != null">#{bReturnTime},</if>
            <if test="dReturnTime != null">#{dReturnTime},</if>
            <if test="mobilePhone != null">#{mobilePhone},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="jgName != null">#{jgName},</if>
            <if test="jgStatus != null">#{jgStatus},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="plateNo != null">#{plateNo},</if>
            <if test="slippageStatus != null">#{slippageStatus},</if>
            <if test="followUp != null">#{followUp},</if>
            <if test="followStatus != null">#{followStatus},</if>
            <if test="certId != null">#{certId},</if>
            <if test="carStatus != null">#{carStatus},</if>
            <if test="gpsStatus != null">#{gpsStatus},</if>
            <if test="carDetailAddress != null">#{carDetailAddress},</if>
            <if test="urgeUser != null">#{urgeUser},</if>
            <if test="urgeTime != null">#{urgeTime},</if>
            <if test="dhAssignmentType != null">#{dhAssignmentType},</if>
            <if test="urgeAssignmentType != null">#{urgeAssignmentType},</if>
            <if test="petitionAssignmentType != null">#{petitionAssignmentType},</if>
            <if test="lawAssignmentType != null">#{lawAssignmentType},</if>
            <if test="petitionUser != null">#{petitionUser},</if>
        </trim>
    </insert>

   <update id="updateVwAccountLoan" parameterType="VwAccountLoan">
       update vw_account_loan
        <trim prefix="SET" suffixOverrides=",">
            <if test="applyId != null">apply_id = #{applyId},</if>
            <if test="putoutId != null">putout_id = #{putoutId},</if>
            <if test="contractId != null">contract_id = #{contractId},</if>
            <if test="customerId != null">customer_id = #{customerId},</if>
            <if test="customerName != null">customer_name = #{customerName},</if>
            <if test="loanStatus != null">loan_status = #{loanStatus},</if>
            <if test="occurType != null">occur_type = #{occurType},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="partnerId != null">partner_id = #{partnerId},</if>
            <if test="currency != null">currency = #{currency},</if>
            <if test="businessSum != null">business_sum = #{businessSum},</if>
            <if test="contractAmt != null">contract_amt = #{contractAmt},</if>
            <if test="putoutDate != null">putout_date = #{putoutDate},</if>
            <if test="billDate != null">bill_date = #{billDate},</if>
            <if test="firstRepayDate != null">first_repay_date = #{firstRepayDate},</if>
            <if test="lastDueDate != null">last_due_date = #{lastDueDate},</if>
            <if test="nextDueDate != null">next_due_date = #{nextDueDate},</if>
            <if test="maturityDate != null">maturity_date = #{maturityDate},</if>
            <if test="term != null">term = #{term},</if>
            <if test="termUnit != null">term_unit = #{termUnit},</if>
            <if test="settleDate != null">settle_date = #{settleDate},</if>
            <if test="finishDate != null">finish_date = #{finishDate},</if>
            <if test="originalMaturityDate != null">original_maturity_date = #{originalMaturityDate},</if>
            <if test="rateTermId != null">rate_term_id = #{rateTermId},</if>
            <if test="rptTermId != null">rpt_term_id = #{rptTermId},</if>
            <if test="finTermId != null">fin_term_id = #{finTermId},</if>
            <if test="feeTermId != null">fee_term_id = #{feeTermId},</if>
            <if test="normalBalance != null">normal_balance = #{normalBalance},</if>
            <if test="nextInstalmentAmt != null">next_instalment_amt = #{nextInstalmentAmt},</if>
            <if test="currentPeriod != null">current_period = #{currentPeriod},</if>
            <if test="currentBalance != null">current_balance = #{currentBalance},</if>
            <if test="currenctinteBalance != null">currenctinte_balance = #{currenctinteBalance},</if>
            <if test="overdueBalance != null">overdue_balance = #{overdueBalance},</if>
            <if test="odinteBalance != null">odinte_balance = #{odinteBalance},</if>
            <if test="fineinteBalance != null">fineinte_balance = #{fineinteBalance},</if>
            <if test="compdinteBalance != null">compdinte_balance = #{compdinteBalance},</if>
            <if test="overdueAmt != null">overdue_amt = #{overdueAmt},</if>
            <if test="overdueDays != null">overdue_days = #{overdueDays},</if>
            <if test="lcaTimes != null">lca_times = #{lcaTimes},</if>
            <if test="totalPeriod != null">total_period = #{totalPeriod},</if>
            <if test="graceinteBalance != null">graceinte_balance = #{graceinteBalance},</if>
            <if test="accrueinteBalance != null">accrueinte_balance = #{accrueinteBalance},</if>
            <if test="repriceType != null">reprice_type = #{repriceType},</if>
            <if test="repriceFlag != null">reprice_flag = #{repriceFlag},</if>
            <if test="repriceCycle != null">reprice_cycle = #{repriceCycle},</if>
            <if test="repriceDate != null">reprice_date = #{repriceDate},</if>
            <if test="lastRepriceDate != null">last_reprice_date = #{lastRepriceDate},</if>
            <if test="nextRepriceDate != null">next_reprice_date = #{nextRepriceDate},</if>
            <if test="graceDays != null">grace_days = #{graceDays},</if>
            <if test="loanOverDateFlag != null">loan_over_date_flag = #{loanOverDateFlag},</if>
            <if test="holidayPaymentFlag != null">holiday_payment_flag = #{holidayPaymentFlag},</if>
            <if test="autoPayFlag != null">auto_pay_flag = #{autoPayFlag},</if>
            <if test="interestTypeFlag != null">interest_type_flag = #{interestTypeFlag},</if>
            <if test="classifyResult != null">classify_result = #{classifyResult},</if>
            <if test="batchNo != null">batch_no = #{batchNo},</if>
            <if test="lockFlag != null">lock_flag = #{lockFlag},</if>
            <if test="businessDate != null">business_date = #{businessDate},</if>
            <if test="putoutRate != null">putout_rate = #{putoutRate},</if>
            <if test="putoutPaymentMethod != null">putout_payment_method = #{putoutPaymentMethod},</if>
            <if test="loanRate != null">loan_rate = #{loanRate},</if>
            <if test="managementFeeRate != null">management_fee_rate = #{managementFeeRate},</if>
            <if test="paymentFlag != null">payment_flag = #{paymentFlag},</if>
            <if test="performanceStatus != null">performance_status = #{performanceStatus},</if>
            <if test="orgId != null">org_id = #{orgId},</if>
            <if test="officeId != null">office_id = #{officeId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="mangerId != null">manger_id = #{mangerId},</if>
            <if test="allotStatus != null">allot_status = #{allotStatus},</if>
            <if test="allotAssignee != null">allot_assignee = #{allotAssignee},</if>
            <if test="litigationStatus != null">litigation_status = #{litigationStatus},</if>
            <if test="subType != null">sub_type = #{subType},</if>
            <if test="numberStatus != null">number_status = #{numberStatus},</if>
            <if test="lagFlag != null">lag_flag = #{lagFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="fOverdueAmount != null">F_overdue_amount = #{fOverdueAmount},</if>
            <if test="bOverdueDays != null">B_overdue_days = #{bOverdueDays},</if>
            <if test="bOverdueAmount != null">B_overdue_amount = #{bOverdueAmount},</if>
            <if test="dOverdueDays != null">D_overdue_days = #{dOverdueDays},</if>
            <if test="dOverdueAmount != null">D_overdue_amount = #{dOverdueAmount},</if>
            <if test="bRepaymentDate != null">B_repayment_date = #{bRepaymentDate},</if>
            <if test="dRepaymentDate != null">D_repayment_date = #{dRepaymentDate},</if>
            <if test="bPeriods != null">B_periods = #{bPeriods},</if>
            <if test="dPeriods != null">D_periods = #{dPeriods},</if>
            <if test="bRemainingAmounts != null">B_remaining_amounts = #{bRemainingAmounts},</if>
            <if test="dRemainingAmounts != null">D_remaining_amounts = #{dRemainingAmounts},</if>
            <if test="bCurrentPeriods != null">B_current_periods = #{bCurrentPeriods},</if>
            <if test="dCurrentPeriods != null">D_current_periods = #{dCurrentPeriods},</if>
            <if test="bRepaymentAmounts != null">B_repayment_amounts = #{bRepaymentAmounts},</if>
            <if test="dRepaymentAmounts != null">D_repayment_amounts = #{dRepaymentAmounts},</if>
            <if test="bNowMoney != null">B_now_money = #{bNowMoney},</if>
            <if test="dNowMoney != null">D_now_money = #{dNowMoney},</if>
            <if test="bReturnTime != null">B_return_time = #{bReturnTime},</if>
            <if test="dReturnTime != null">D_return_time = #{dReturnTime},</if>
            <if test="mobilePhone != null">mobile_phone = #{mobilePhone},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="jgName != null">jg_name = #{jgName},</if>
            <if test="jgStatus != null">jg_status = #{jgStatus},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="plateNo != null">plate_no = #{plateNo},</if>
            <if test="slippageStatus != null">slippage_status = #{slippageStatus},</if>
            <if test="followUp != null">follow_up = #{followUp},</if>
            <if test="followStatus != null">follow_status = #{followStatus},</if>
            <if test="certId != null">cert_id = #{certId},</if>
            <if test="carStatus != null">car_status = #{carStatus},</if>
            <if test="gpsStatus != null">gps_status = #{gpsStatus},</if>
            <if test="carDetailAddress != null">car_detail_address = #{carDetailAddress},</if>
            <if test="urgeUser != null">urge_user = #{urgeUser},</if>
            <if test="urgeTime != null">urge_time = #{urgeTime},</if>
            <if test="dhAssignmentType != null">dh_assignment_type = #{dhAssignmentType},</if>
            <if test="urgeAssignmentType != null">urge_assignment_type = #{urgeAssignmentType},</if>
            <if test="petitionAssignmentType != null">petition_assignment_type = #{petitionAssignmentType},</if>
            <if test="lawAssignmentType != null">law_assignment_type = #{lawAssignmentType},</if>
            <if test="petitionUser != null">petition_user = #{petitionUser},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVwAccountLoanById" parameterType="String">
        delete from vw_account_loan where id = #{id}
    </delete>

    <delete id="deleteVwAccountLoanByIds" parameterType="String">
        delete from vw_account_loan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>