# Apply_ID 支持多个 Loan_ID 修改说明

## 修改概述

将原有的一个 apply_id 对应一个 loan_id 的关系修改为一个 apply_id 可以对应多个 loan_id 的关系。

## 修改内容

### 1. 后端控制器修改 (VwAccountLoanController.java)

#### 修改的方法：
- `replayList()` - 查询还款计划方法

#### 修改前的逻辑：
- 直接使用传入的 partner_id 查询还款计划
- 假设一个 apply_id 只对应一个 loan_id

#### 修改后的逻辑：
- 首先根据 apply_id 查询所有相关的 loan 记录
- 从查询结果中获取 partner_id（假设同一个 apply_id 下的所有 loan 都是同一个银行）
- 根据 partner_id 查询对应的还款计划
- 支持一个 apply_id 对应多个 loan_id 的情况

#### 新增的方法：

1. **getLoansByApplyId(String applyId)**
   - 路径：`GET /vw_account_loan/vw_account_loan/loansByApplyId/{applyId}`
   - 功能：根据 apply_id 获取所有相关的 loan 记录列表
   - 返回：List<VwAccountLoan>

2. **getSummaryByApplyId(String applyId)**
   - 路径：`GET /vw_account_loan/vw_account_loan/summaryByApplyId/{applyId}`
   - 功能：根据 apply_id 获取汇总信息（包含 loan 数量、总金额等）
   - 返回：包含以下信息的 Map：
     - applyId: 申请ID
     - loanCount: loan数量
     - loanList: loan列表
     - partnerId: 银行ID
     - customerName: 客户名称
     - customerId: 客户ID

### 2. 前端 API 修改 (vw_account_loan.js)

#### 新增的方法：

1. **getLoansByApplyId(applyId)**
   - 功能：根据 apply_id 获取所有相关的 loan 记录列表

2. **getSummaryByApplyId(applyId)**
   - 功能：根据 apply_id 获取汇总信息

## 数据库关系说明

### 表关系：
- `vw_account_loan` 表：包含 apply_id 和 loan_id 字段
- `loan_list` 表：包含 apply_id 和 id 字段（id 就是 loan_id）
- 还款计划表（如 `sy_repay_plan` 等）：通过 apply_id 关联

### 关系变化：
- **修改前**：apply_id : loan_id = 1 : 1
- **修改后**：apply_id : loan_id = 1 : N

## 使用示例

### 后端调用示例：

```java
// 查询还款计划（支持多个loan_id）
VwAccountLoan condition = new VwAccountLoan();
condition.setApplyId("APPLY123456");
TableDataInfo result = controller.replayList(condition);

// 获取apply_id下的所有loan记录
AjaxResult loans = controller.getLoansByApplyId("APPLY123456");

// 获取apply_id的汇总信息
AjaxResult summary = controller.getSummaryByApplyId("APPLY123456");
```

### 前端调用示例：

```javascript
// 获取apply_id下的所有loan记录
getLoansByApplyId('APPLY123456').then(response => {
  console.log('Loan列表:', response.data);
});

// 获取apply_id的汇总信息
getSummaryByApplyId('APPLY123456').then(response => {
  console.log('汇总信息:', response.data);
  console.log('Loan数量:', response.data.loanCount);
});
```

## 注意事项

1. **向后兼容性**：修改保持了向后兼容，原有的单个 loan_id 查询仍然有效
2. **银行一致性**：假设同一个 apply_id 下的所有 loan 都属于同一个银行（partner_id 相同）
3. **错误处理**：增加了对空数据和无效参数的处理
4. **日志记录**：添加了适当的日志记录用于调试

## 测试建议

1. 测试单个 apply_id 对应单个 loan_id 的情况（向后兼容性）
2. 测试单个 apply_id 对应多个 loan_id 的情况
3. 测试无效 apply_id 的错误处理
4. 测试不同银行（partner_id）的还款计划查询

## 影响范围

- 后端：VwAccountLoanController.java
- 前端：vw_account_loan.js
- 数据库：无结构变更，仅查询逻辑调整
