import request from '@/utils/request'

// 查询VIEW列表
export function listVw_account_loan(query) {
  return request({
    url: '/vw_account_loan/vw_account_loan/list',
    method: 'get',
    params: query,
  })
}

//查询上访逾期
export function listVw_account_loan_slippage3(query) {
  return request({
    url: '/vw_account_loan/vw_account_loan/listBySlippageStatus3',
    method: 'get',
    params: query,
  })
}

// 查询VIEW详细
export function getVw_account_loan(id) {
  return request({
    url: '/vw_account_loan/vw_account_loan/' + id,
    method: 'get',
  })
}

// 通过loanId查询VIEW详细
export function getVw_account_loan_by_loanId(loanId) {
  return request({
    url: '/vw_account_loan/vw_account_loan/byLoanId/' + loanId,
    method: 'get',
  })
}

// 查询录单渠道、找车团队
export function teamVm_car_order() {
  return request({
    url: '/sys_office/sys_office/list',
    method: 'get',
  })
}
// 银行账户列表
export function get_bank_account() {
  return request({
    url: '/bank_account/bank_account/bank?pageSize=30',
    method: 'get',
  })
}
// 代偿
export function loan_compensation_order(data) {
  return request({
    url: '/loan_compensation/loan_compensation/detail',
    method: 'get',
    params: data,
  })
}
// 查询车牌详细信息
export function check_car_order(id) {
  return request({
    url: '/vw_car/vw_car/' + id,
    method: 'get',
  })
}
// 催记列表
export function loan_reminder_order(data) {
  return request({
    url: '/loan_reminder/loan_reminder/list',
    method: 'get',
    params: data,
  })
}
// 提交催记
export function loan_reminder_post(data) {
  return request({
    url: '/loan_reminder/loan_reminder',
    method: 'post',
    data: data,
  })
}
// 还款计划
export function plan_order(data) {
  return request({
    url: '/vw_account_loan/vw_account_loan/replayList',
    method: 'get',
    params: data,
  })
}
// 新增VIEW
export function addVw_account_loan(data) {
  return request({
    url: '/vw_account_loan/vw_account_loan',
    method: 'post',
    data: data,
  })
}
// 提交催记
export function loan_reminder(data) {
  return request({
    url: '/loan_reminder/loan_reminder',
    method: 'post',
    data: data,
  })
}

// 查询催记详情
export function loan_reminder_detail(data) {
  return request({
    url: '/loan_reminder/loan_reminder/detail',
    method: 'get',
    params: data,
  })
}

// 修改VIEW
export function updateVw_account_loan(data) {
  return request({
    url: '/vw_account_loan/vw_account_loan',
    method: 'put',
    data: data,
  })
}

// 删除VIEW
export function delVw_account_loan(id) {
  return request({
    url: '/vw_account_loan/vw_account_loan/' + id,
    method: 'delete',
  })
}

// 贷款人信息
export function lender_order(customerId, applyId) {
  return request({
    url: '/vw_customer_info/vw_customer_info/' + customerId + '?applyNo=' + applyId,
    method: 'get',
  })
}

// 贷款人信息
export function trial_balance_order(data) {
  return request({
    url: '/loan_settle/loan_settle/detail',
    method: 'get',
    params: data,
  })
}

// 贷款人信息
export function trial_submit_order(data) {
  return request({
    url: '/trial_balance/trial_balance/submit',
    method: 'post',
    data: data,
  })
}

// 代偿信息
export function dc_submit_order(data) {
  return request({
    url: '/trial_balance/trial_balance/submitdc',
    method: 'post',
    data: data,
  })
}

// 新增结清记录
export function trial_balance_post(data) {
  return request({
    url: '/loan_settle/loan_settle',
    method: 'post',
    data: data,
  })
}

// 新增代偿结清记录
export function add_Trial_order_dc(loanId, status) {
  return request({
    url: '/loan_settle/loan_settle/process',
    method: 'post',
    data: {
      loanId: loanId,
      status: status
    }
  })
}

// 新增代偿
export function add_Trial_order(data) {
  return request({
    url: '/loan_compensation/loan_compensation/initiate',
    method: 'post',
    data: data,
  })
}

// 提交代偿
export function sub_Trial_order(data) {
  return request({
    url: '/loan_compensation/loan_compensation',
    method: 'put',
    data: data,
  })
}

// 修改结清记录
export function trial_balance_put(data) {
  return request({
    url: '/loan_settle/loan_settle',
    method: 'put',
    data: data,
  })
}

//获取电催员列表
export function get_dcc_list(roleId) {
  return request({
    url: '/system/user/listByRoleId',
    method: 'get',
    params: { roleId }  // 这里用 params，roleId 是参数名
  })
}

//分配电催员
export function loan_extension_dcc(data) {
  return request({
    url: '/loan_list/loan_list/batchUpdateUrgeUser',
    method: 'put',
    data: data,
  })
}

// 预估呆账
export function update_loan_list(data) {
  return request({
    url: '/loan_list/loan_list',
    method: 'put',
    data: data,
  })
}

// 查询VIEW申请延期列表
export function listVw_account_loan_extension(query) {
  return request({
    url: '/vw_account_loan/vw_account_loan/extension_list',
    method: 'get',
    params: query,
  })
}

// 查询延期申请详情
export function loan_extension_detail(loanId) {
  return request({
    url: '/loan_extension/loan_extension/extension_detail',
    method: 'get',
    params: {
      loan_id: loanId,
    },
  })
}

// 延期审批提交
export function loan_extension_approval(data) {
  return request({
    url: '/loan_extension/loan_extension/approve',
    method: 'put',
    data: data,
  })
}

export function batchAssignPetitionUser(data) {
  return request({
    url: '/loan_list/loan_list/batchAssignPetitionUser',
    method: 'put',
    data
  })
}

export function revokePetitionUser(id) {
  return request({
    url: `/loan_list/loan_list/revokePetitionUser/${id}`,
    method: 'put'
  })
}

// 根据apply_id获取所有相关的loan记录列表
export function getLoansByApplyId(applyId) {
  return request({
    url: '/vw_account_loan/vw_account_loan/loansByApplyId/' + applyId,
    method: 'get'
  })
}

// 根据apply_id获取汇总信息（包含loan数量、总金额等）
export function getSummaryByApplyId(applyId) {
  return request({
    url: '/vw_account_loan/vw_account_loan/summaryByApplyId/' + applyId,
    method: 'get'
  })
}
