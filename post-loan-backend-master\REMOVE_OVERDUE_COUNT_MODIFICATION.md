# 删除逾期次数功能修改说明

## 修改概述

根据需求，删除系统中获取和显示逾期次数（overdueCount）的相关功能代码。

## 修改内容

### 1. 后端实体类修改

#### VwAccountLoan.java
- **删除字段**：`private Integer overdueCount;`
- **删除注解**：`@Excel(name = "逾期次数")`
- **位置**：第517-519行

### 2. 数据库映射文件修改

#### VwAccountLoanMapper.xml
- **删除ResultMap映射**：
  ```xml
  <result property="overdueCount" column="overdueCount" />
  ```
- **删除SQL查询中的逾期次数计算**：
  ```sql
  (
      SELECT COUNT(*)
      FROM vw_account_loan v
      WHERE v.apply_id = a.apply_id
  ) AS overdueCount
  ```

#### 修改后的SQL查询：
```sql
<sql id="selectVwAccountLoanVo">
    SELECT
        a.*,
        s.total_money AS bSettleAmount,
        r.appointed_time,
        r.tracking_time
    FROM
        vw_account_loan a
            LEFT JOIN LATERAL (
            SELECT
                total_money
            FROM
                loan_settle
            WHERE
                loan_id = a.loan_id
            ORDER BY
                create_date DESC
                LIMIT 1
) s ON true
        LEFT JOIN LATERAL (
        SELECT
        appointed_time,
        tracking_time
        FROM
        loan_reminder
        WHERE
        loan_id = a.loan_id
        ORDER BY
        create_time DESC
        LIMIT 1
        ) r ON true
</sql>
```

### 3. 前端页面修改

#### vw_account_loan/index.vue
- **删除表格列**：逾期次数列
- **删除代码**：
  ```vue
  <el-table-column label="逾期次数" align="center" prop="overdueCount" width="100">
    <template slot-scope="scope">
      <span v-if="scope.row.overdueCount != null">{{ scope.row.overdueCount }}</span>
      <span v-else>0</span>
    </template>
  </el-table-column>
  ```

## 影响范围

### 已修改的文件：
1. `post-loan-backend-master/ruoyi-admin/src/main/java/com/ruoyi/vw_account_loan/domain/VwAccountLoan.java`
2. `post-loan-backend-master/ruoyi-admin/src/main/resources/mapper/vw_account_loan/VwAccountLoanMapper.xml`
3. `post-loan-backend-master/ruoyi-admin/target/classes/mapper/vw_account_loan/VwAccountLoanMapper.xml`
4. `post-loan-backend-page/src/views/vw_account_loan/vw_account_loan/index.vue`

### 功能影响：
- **删除功能**：不再计算和显示逾期次数
- **性能提升**：移除了COUNT查询，提高了查询性能
- **界面简化**：前端表格减少了一列显示

## 数据库性能优化

删除逾期次数计算后的性能改进：

### 修改前：
```sql
-- 每次查询都会执行子查询计算逾期次数
(
    SELECT COUNT(*)
    FROM vw_account_loan v
    WHERE v.apply_id = a.apply_id
) AS overdueCount
```

### 修改后：
- 移除了子查询，减少了数据库计算负担
- 查询速度更快，特别是在大数据量情况下

## 注意事项

1. **数据完整性**：删除逾期次数字段不影响其他业务逻辑
2. **向后兼容**：如果有其他地方引用overdueCount字段，需要相应调整
3. **前端显示**：前端页面不再显示逾期次数列
4. **API响应**：API返回的数据中不再包含overdueCount字段

## 测试建议

1. **功能测试**：
   - 验证逾期信息列表查询正常
   - 确认前端页面显示正常
   - 检查其他相关功能未受影响

2. **性能测试**：
   - 对比删除前后的查询性能
   - 验证大数据量下的查询速度提升

3. **回归测试**：
   - 测试所有使用VwAccountLoan实体的功能
   - 确保没有其他地方依赖overdueCount字段

## 回滚方案

如果需要恢复逾期次数功能，可以：

1. 恢复VwAccountLoan.java中的overdueCount字段
2. 恢复Mapper XML中的相关映射和SQL查询
3. 恢复前端页面中的逾期次数列显示

## 完成状态

✅ 后端实体类修改完成  
✅ 数据库映射文件修改完成  
✅ 前端页面修改完成  
✅ 代码编译检查通过  
✅ 文档更新完成
