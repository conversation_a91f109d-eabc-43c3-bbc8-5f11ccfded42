package com.ruoyi.vw_account_loan.controller;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.coborrower_info.domain.CoborrowerInfo;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.hr_repay_plan.domain.HrRepayPlan;
import com.ruoyi.hr_repay_plan.service.IHrRepayPlanService;
import com.ruoyi.lh_repay_plan.domain.LhRepayPlan;
import com.ruoyi.lh_repay_plan.service.ILhRepayPlanService;
import com.ruoyi.loan_extension.domain.LoanExtension;
import com.ruoyi.loan_extension.service.ILoanExtensionService;
import com.ruoyi.loan_extension.service.impl.LoanExtensionServiceImpl;
import com.ruoyi.loan_list.domain.LoanList;
import com.ruoyi.loan_list.service.ILoanListService;
import com.ruoyi.sy_repay_plan.domain.SyRepayPlan;
import com.ruoyi.sy_repay_plan.service.ISyRepayPlanService;
import com.ruoyi.wx_repay_plan.domain.WxRepayPlan;
import com.ruoyi.wx_repay_plan.service.IWxRepayPlanService;
import com.ruoyi.zgc_repay_plan.domain.ZgcRepayPlan;
import com.ruoyi.zgc_repay_plan.service.IZgcRepayPlanService;
import com.ruoyi.zs_repayment_plan.controller.ZsRepaymentPlanController;
import com.ruoyi.zs_repayment_plan.domain.ZsRepaymentPlan;
import com.ruoyi.zs_repayment_plan.service.IZsRepaymentPlanService;
import com.ruoyi.zs_repayment_plan.service.impl.ZsRepaymentPlanServiceImpl;
import com.zrx.image.interfaces.entity.DocFactDTO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.vw_account_loan.domain.VwAccountLoan;
import com.ruoyi.vw_account_loan.service.IVwAccountLoanService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.loan_reminder.domain.LoanReminder;
import org.springframework.web.bind.annotation.RequestParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * VIEWController
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
@RestController
@RequestMapping("/vw_account_loan/vw_account_loan")
public class VwAccountLoanController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(VwAccountLoanController.class);
    @Autowired
    private IVwAccountLoanService vwAccountLoanService;

    // 苏银 还款计划
    @Autowired
    private ISyRepayPlanService syRepayPlanService;

    // 浙商 还款计划
    @Autowired
    private IZsRepaymentPlanService zsRepaymentPlanService;

    // 中关村银行
    @Autowired
    private IZgcRepayPlanService zgcRepayPlanService;

    // 蓝海银行
    @Autowired
    private ILhRepayPlanService lhRepayPlanService;

    // 华瑞银行
    @Autowired
    private IHrRepayPlanService hrRepayPlanService;

    // 皖新租赁
    @Autowired
    private IWxRepayPlanService wxzxLeaseService;

    // 放款列表
    @Autowired
    private ILoanListService loanListService;

    // @Autowired
    // private com.ruoyi.common.service.ImageService imageService;

    /**
     * 查询逾期信息所需要的搜索条件
     */
    @GetMapping("/cate")
    @Anonymous
    public TableDataInfo cate(VwAccountLoan vwAccountLoan) throws Exception {

        // 查询放款银行
        // List<String> bankList = vwAccountLoanService.selectBankList();

        return getDataTable(vwAccountLoanService.selectVwAccountLoanList(vwAccountLoan));
    }

    /**
     * 查询逾期信息列表
     */
    // @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:list')")
    @GetMapping("/list")
    @Anonymous
    public TableDataInfo list(VwAccountLoan vwAccountLoan) throws Exception {
        startPage();
        vwAccountLoan.setOrgId(getOrgId());
        List<VwAccountLoan> list = vwAccountLoanService.selectVwAccountLoanList(vwAccountLoan);

        // 过滤掉已完结的记录（repaymentStatus为2、3、4、5、10、11的状态）
        list = list.stream()
            .filter(loan -> {
                Long status = loan.getRepaymentStatus();
                return status == null ||
                       !(status == 2 || status == 3 || status == 4 ||
                         status == 5 || status == 10 || status == 11);
            })
            .collect(java.util.stream.Collectors.toList());

        // for (VwAccountLoan vwAccountLoan1 : list) {
        // //查询贷款的银行账单还款计划
        // // EO00000010-苏银金租
        // // IO00000006-浙商银行
        // // IO00000007-中关村银行
        // // IO00000008-蓝海银行
        // // IO00000009-华瑞银行
        // // IO00000010-皖新租赁
        //// if (vwAccountLoan1.getPartnerId() != null ){
        //// if (Objects.equals(vwAccountLoan1.getPartnerId(), "EO00000010")){ //
        //// // 构造查询条件（假设根据 applyNo 查询共担保人）
        //// SyRepayPlan query = new SyRepayPlan();
        //// query.setApplyId(vwAccountLoan1.getApplyId()); // 根据实际字段名调整
        //// List<SyRepayPlan> list1 =
        // syRepayPlanService.selectSyRepayPlanList(query);//还款计划
        ////
        //// List<SyRepayPlan> yq = list1.stream() //逾期的数据
        //// .filter(item -> item.getStatus() != null &&
        // Objects.equals(item.getStatus(), "2"))
        //// .sorted(Comparator.comparingInt(o -> Integer.parseInt(o.getStageNum())))
        //// .toList();
        ////
        //// logger.info("逾期的数据：{}", yq);
        ////
        //// }else if (vwAccountLoan1.getPartnerId() == "IO00000006"){
        //// ZsRepaymentPlan query = new ZsRepaymentPlan();
        //// query.setApplyId(vwAccountLoan1.getApplyId());
        //// List<ZsRepaymentPlan> list1 =
        // zsRepaymentPlanService.selectZsRepaymentPlanList(query);
        //// logger.info("查询的list1：{}", list1);
        ////
        //// }else if (vwAccountLoan1.getPartnerId() == "IO00000007") {
        //// ZgcRepayPlan query = new ZgcRepayPlan();
        //// query.setApplyId(vwAccountLoan1.getApplyId());
        //// List<ZgcRepayPlan> list1 =
        // zgcRepayPlanService.selectZgcRepayPlanList(query);
        //// logger.info("查询的list1：{}", list1);
        ////
        //// }else if (vwAccountLoan1.getPartnerId() == "IO00000008") {
        //// LhRepayPlan query = new LhRepayPlan();
        //// query.setApplyId(vwAccountLoan1.getApplyId());
        //// List<LhRepayPlan> list1 = lhRepayPlanService.selectLhRepayPlanList(query);
        //// logger.info("查询的list1：{}", list1);
        ////
        //// }else if (vwAccountLoan1.getPartnerId() == "IO00000009") {
        //// HrRepayPlan query = new HrRepayPlan();
        //// query.setApplyId(vwAccountLoan1.getApplyId());
        //// List<HrRepayPlan> list1 = hrRepayPlanService.selectHrRepayPlanList(query);
        //// logger.info("查询的list1：{}", list1);
        ////
        //// }else if (vwAccountLoan1.getPartnerId() == "IO00000010") {
        //// WxRepayPlan query = new WxRepayPlan();
        //// query.setApplyId(vwAccountLoan1.getApplyId());
        //// List<WxRepayPlan> list1 = wxzxLeaseService.selectWxRepayPlanList(query);
        //// logger.info("查询的list1：{}", list1);
        ////
        //// }
        //// }
        // }

        return getDataTable(list);
    }

    /**
     * 查询slippageStatus为3的列表
     */
    @GetMapping("/listBySlippageStatus3")
    @Anonymous
    public TableDataInfo listBySlippageStatus3(VwAccountLoan vwAccountLoan, @RequestParam(required = false) String[] allocationTime) {
        vwAccountLoan.setSlippageStatus("3"); // slippageStatus 字段为 String 类型
        if (allocationTime != null && allocationTime.length == 2) {
            vwAccountLoan.setStartTime(allocationTime[0]);
            vwAccountLoan.setEndTime(allocationTime[1]);
            logger.info("startTime={}, endTime={}", allocationTime[0], allocationTime[1]);
        } else {
            logger.info("allocationTime is null or length != 2");
        }
        startPage();
        List<VwAccountLoan> list = vwAccountLoanService.selectVwAccountLoanList(vwAccountLoan);
        // 补充每条数据的最新催记
        for (VwAccountLoan loan : list) {
            LoanReminder reminder = ((com.ruoyi.vw_account_loan.service.impl.VwAccountLoanServiceImpl)vwAccountLoanService).getLatestLoanReminderByLoanId(loan.getLoanId());
            loan.setLoanReminder(reminder);
        }
        return getDataTable(list);
    }

    /**
     * 查询贷后逾期记录列表
     */
    @PreAuthorize("@ss.hasPermi('loan_list:loan_list:list')")
    @GetMapping("/extension_list")
    public TableDataInfo extensionList(VwAccountLoan vwAccountLoan, @RequestParam(value = "followUpType", required = false) Integer followUpType) {
        startPage();
        List<VwAccountLoan> list = vwAccountLoanService.selectVwAccountLoanExtensionList(vwAccountLoan);
        // 如果前端传了followUpType，则只保留loanReminder.urgeStatus等于该值的数据
        if (followUpType != null) {
            list.removeIf(loan -> loan.getLoanReminder() == null || loan.getLoanReminder().getUrgeStatus() == null || !followUpType.equals(loan.getLoanReminder().getUrgeStatus()));
        }
        return getDataTable(list);
    }

    /**
     * 查询还款计划
     */

    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:list')")
    @GetMapping("/replayList")
    // @Anonymous
    public TableDataInfo replayList(VwAccountLoan vwAccountLoan1) throws Exception {
        if (vwAccountLoan1.getPartnerId() != null) {
            switch (vwAccountLoan1.getPartnerId()) {
                case "EO00000010" -> { //
                    // 构造查询条件（假设根据 applyNo 查询共担保人）
                    SyRepayPlan query = new SyRepayPlan();
                    query.setApplyId(vwAccountLoan1.getApplyId()); // 根据实际字段名调整
                    startPage();
                    List<SyRepayPlan> list1 = syRepayPlanService.selectSyRepayPlanList(query);// 还款计划
                    return getDataTable(list1);
                }
                case "IO00000006" -> {
                    ZsRepaymentPlan query = new ZsRepaymentPlan();
                    query.setApplyId(vwAccountLoan1.getApplyId());
                    startPage();
                    List<ZsRepaymentPlan> list1 = zsRepaymentPlanService.selectZsRepaymentPlanList(query);
                    return getDataTable(list1);

                }
                case "IO00000007" -> {
                    ZgcRepayPlan query = new ZgcRepayPlan();
                    query.setApplyId(vwAccountLoan1.getApplyId());
                    startPage();
                    List<ZgcRepayPlan> list1 = zgcRepayPlanService.selectZgcRepayPlanList(query);
                    return getDataTable(list1);

                }
                case "IO00000008" -> {
                    LhRepayPlan query = new LhRepayPlan();
                    query.setApplyId(vwAccountLoan1.getApplyId());
                    startPage();
                    List<LhRepayPlan> list1 = lhRepayPlanService.selectLhRepayPlanList(query);
                    return getDataTable(list1);

                }
                case "IO00000009" -> {
                    HrRepayPlan query = new HrRepayPlan();
                    query.setApplyId(vwAccountLoan1.getApplyId());
                    startPage();
                    List<HrRepayPlan> list1 = hrRepayPlanService.selectHrRepayPlanList(query);
                    return getDataTable(list1);

                }
                case "IO00000010" -> {
                    WxRepayPlan query = new WxRepayPlan();
                    query.setApplyId(vwAccountLoan1.getApplyId());
                    startPage();
                    List<WxRepayPlan> list1 = wxzxLeaseService.selectWxRepayPlanList(query);
                    return getDataTable(list1);

                }
                default -> {
                    return getDataTable(null);

                }
            }
        } else {
            return getDataTable(null);
        }
    }

    /**
     * 导出VIEW列表
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:export')")
    @Log(title = "VIEW", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, VwAccountLoan vwAccountLoan) {
        List<VwAccountLoan> list = vwAccountLoanService.selectVwAccountLoanList(vwAccountLoan);
        ExcelUtil<VwAccountLoan> util = new ExcelUtil<VwAccountLoan>(VwAccountLoan.class);
        util.exportExcel(response, list, "VIEW数据");
    }

    /**
     * 获取VIEW详细信息
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(vwAccountLoanService.selectVwAccountLoanById(id));
    }

    /**
     * 根据loanId获取VIEW详细信息
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:query')")
    @GetMapping(value = "/byLoanId/{loanId}")
    public AjaxResult getInfoByLoanId(@PathVariable("loanId") String loanId) {
        VwAccountLoan vwAccountLoan = vwAccountLoanService.selectVwAccountLoanByLoanId(loanId);
        if (vwAccountLoan == null) {
            return error("未找到loanId为" + loanId + "的记录");
        }
        return success(vwAccountLoan);
    }

    /**
     * 获取最新的电催员催记信息
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:query')")
    @GetMapping(value = "/latestCollectionReminder/{loanId}")
    public AjaxResult getLatestCollectionReminder(@PathVariable("loanId") Long loanId) {
        if (loanId == null) {
            return error("贷款ID不能为空");
        }

        LoanReminder reminder = vwAccountLoanService.selectLatestCollectionOfficerReminderByLoanId(loanId);
        if (reminder != null) {
            // 可以在这里对数据进行转换或处理，例如格式化日期等
            return success(reminder);
        } else {
            return success(null);
        }
    }

    /**
     * 获取附带最新电催员催记信息的贷款详情
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:query')")
    @GetMapping(value = "/loanWithCollectionInfo/{loanId}")
    public AjaxResult getLoanWithCollectionInfo(@PathVariable("loanId") String loanId) {
        if (loanId == null || loanId.trim().isEmpty()) {
            return error("贷款ID不能为空");
        }

        VwAccountLoan vwAccountLoan = vwAccountLoanService.selectVwAccountLoanByLoanId(loanId);
        if (vwAccountLoan == null) {
            return error("未找到loanId为" + loanId + "的记录");
        }

        return success(vwAccountLoan);
    }

    /**
     * 新增VIEW
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:add')")
    @Log(title = "VIEW", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody VwAccountLoan vwAccountLoan) {
        return toAjax(vwAccountLoanService.insertVwAccountLoan(vwAccountLoan));
    }

    /**
     * 修改VIEW
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:edit')")
    @Log(title = "VIEW", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody VwAccountLoan vwAccountLoan) {
        return toAjax(vwAccountLoanService.updateVwAccountLoan(vwAccountLoan));
    }

    /**
     * 删除VIEW
     */
    @PreAuthorize("@ss.hasPermi('vw_account_loan:vw_account_loan:remove')")
    @Log(title = "VIEW", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(vwAccountLoanService.deleteVwAccountLoanByIds(ids));
    }
}
